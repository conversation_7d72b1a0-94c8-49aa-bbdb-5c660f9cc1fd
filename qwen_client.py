"""
Qwen-VL-Plus API客户端
"""
import requests
import time
from typing import Dict, Any, <PERSON><PERSON>
from config import QWEN_API_URL, QWEN_API_KEY, QWEN_MODEL, REQUEST_TIMEOUT
from logger import APILogger

class QwenClient:
    """Qwen-VL-Plus API客户端"""
    
    def __init__(self):
        self.api_url = QWEN_API_URL
        self.api_key = QWEN_API_KEY
        self.model = QWEN_MODEL
        
        if not self.api_key:
            raise ValueError("QWEN_API_KEY环境变量未设置")
    
    def analyze_image(self, image_url: str) -> Tuple[str, float, Dict[str, int], Dict[str, Any]]:
        """
        分析图片中的考试题目
        
        Args:
            image_url: 图片URL
            
        Returns:
            Tuple[str, float, Dict[str, int], Dict[str, Any]]: (识别结果, 响应时间, token使用情况, 原始响应数据)
        """
        APILogger.log_qwen_request(image_url)
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "image": image_url
                            },
                            {
                                "text": """请精准识别图片中的考试题目内容。图片是考试系统的截图，包含考试题目和选项。

请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）"""
                            }
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 提取结果
                result_text = response_data.get("output", {}).get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # 提取token使用情况
                usage = response_data.get("usage", {})
                token_usage = {
                    "input_tokens": usage.get("input_tokens", 0),
                    "output_tokens": usage.get("output_tokens", 0),
                    "total_tokens": usage.get("total_tokens", 0)
                }
                
                APILogger.log_qwen_response(response_data, response_time, token_usage)

                return result_text, response_time, token_usage, response_data
            else:
                error_msg = f"Qwen API请求失败: {response.status_code} - {response.text}"
                APILogger.log_error(error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Qwen API请求异常: {str(e)}"
            APILogger.log_error(error_msg)
            raise Exception(error_msg)
