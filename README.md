# 考试题目处理系统

这是一个基于Python的考试题目处理系统，使用qwen-vl-plus进行图片识别，使用deepseek-chat进行题目解答。

## 功能特性

- 使用qwen-vl-plus识别考试图片中的题目内容
- 使用deepseek-chat提供准确的答案和解析
- 完整的日志记录，包括响应时间和token消耗统计
- 支持单选题、多选题、判断题三种题型
- **返回原始数据**：同时返回qwen和deepseek的完整原始响应数据

## 安装和配置

### 1. 创建虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置API密钥
复制`.env.example`为`.env`并填入真实的API密钥：
```bash
cp .env.example .env
```

编辑`.env`文件：
```
QWEN_API_KEY=your_qwen_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

## 使用方法

### 方式一：命令行使用
```bash
# 激活虚拟环境
source venv/bin/activate

# 运行程序
python main.py <图片URL>
```

示例：
```bash
python main.py https://example.com/exam_image.jpg
```

### 方式二：Web API使用

#### 启动API服务器
```bash
# 使用启动脚本（推荐）
./start_server.sh

# 或手动启动
source venv/bin/activate
python api_server.py
```

#### cURL测试命令

```bash
# 健康检查
curl -X GET http://localhost:5000/health

# 处理考试图片
curl -X POST http://localhost:5000/process_exam \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/exam_image.jpg"
  }'
```

更多cURL测试命令请参考 `test_curl_commands.md` 文件。

## 项目结构

```
.
├── main.py                 # 主程序入口（命令行版本）
├── api_server.py          # Web API服务器
├── qwen_client.py         # Qwen-VL-Plus API客户端
├── deepseek_client.py     # DeepSeek-Chat API客户端
├── logger.py              # 日志记录模块
├── config.py              # 配置文件
├── test_system.py         # 系统测试脚本
├── start_server.sh        # API服务器启动脚本
├── requirements.txt       # Python依赖
├── .env.example           # 环境变量示例
├── test_curl_commands.md  # cURL测试命令说明
├── README.md             # 说明文档
├── s1.md                 # 原始需求文档
├── venv/                 # Python虚拟环境
└── exam_processor.log    # 日志文件（运行后生成）
```

## 日志记录

系统会记录以下信息：
- qwen-vl-plus的请求和响应详情
- deepseek-chat的请求和响应详情
- 两个模型的响应时间和token消耗
- 最终的答案和解析结果
- 错误信息

日志会同时输出到控制台和`exam_processor.log`文件。

## 响应数据格式

API响应包含以下数据：
- **处理结果**：解析后的答案和解析
- **性能数据**：响应时间和token使用统计
- **原始数据**：qwen和deepseek的完整原始响应（新增功能）

这样可以让请求者获得完整的API调用信息，便于调试和分析。

## 支持的题型

1. **单选题**：A、B、C、D选项
2. **多选题**：多个正确选项
3. **判断题**：Y（正确）、N（错误）

## 注意事项

1. 确保图片URL可以公开访问
2. 图片应该清晰，包含完整的题目和选项
3. 需要有效的qwen和deepseek API密钥
4. 网络连接稳定，API调用可能需要一些时间
