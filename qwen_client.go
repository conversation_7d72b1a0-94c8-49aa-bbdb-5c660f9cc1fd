package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// QwenClient Qwen-VL-Plus API客户端
type QwenClient struct {
	APIURL string
	APIKey string
	Model  string
}

// TokenUsage token使用情况
type TokenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// QwenResponse qwen API响应结构
type QwenResponse struct {
	Output struct {
		Choices []struct {
			FinishReason string `json:"finish_reason"`
			Message      struct {
				Role    string `json:"role"`
				Content []struct {
					Text string `json:"text"`
				} `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage     TokenUsage `json:"usage"`
	RequestID string     `json:"request_id"`
}

// QwenRequest qwen API请求结构
type QwenRequest struct {
	Model string `json:"model"`
	Input struct {
		Messages []struct {
			Role    string `json:"role"`
			Content []struct {
				Image string `json:"image,omitempty"`
				Text  string `json:"text,omitempty"`
			} `json:"content"`
		} `json:"messages"`
	} `json:"input"`
	Parameters struct {
		ResultFormat string `json:"result_format"`
	} `json:"parameters"`
}

// AnalyzeImageResult 分析结果
type AnalyzeImageResult struct {
	Text         string
	ResponseTime float64
	TokenUsage   TokenUsage
	RawResponse  QwenResponse
}

// NewQwenClient 创建新的Qwen客户端
func NewQwenClient() (*QwenClient, error) {
	apiKey := os.Getenv("QWEN_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("QWEN_API_KEY环境变量未设置")
	}

	return &QwenClient{
		APIURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		APIKey: apiKey,
		Model:  "qwen-vl-plus",
	}, nil
}

// AnalyzeImage 分析图片中的考试题目
func (c *QwenClient) AnalyzeImage(imageURL string) (*AnalyzeImageResult, error) {
	fmt.Printf("发送请求到qwen-vl-plus，图片URL: %s\n", imageURL)

	// 构建请求payload
	request := QwenRequest{
		Model: c.Model,
	}

	request.Input.Messages = []struct {
		Role    string `json:"role"`
		Content []struct {
			Image string `json:"image,omitempty"`
			Text  string `json:"text,omitempty"`
		} `json:"content"`
	}{
		{
			Role: "user",
			Content: []struct {
				Image string `json:"image,omitempty"`
				Text  string `json:"text,omitempty"`
			}{
				{Image: imageURL},
				{Text: `请精准识别图片中的考试题目内容。图片是考试系统的截图，包含考试题目和选项。

请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）`},
			},
		},
	}

	request.Parameters.ResultFormat = "message"

	// 转换为JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", c.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// 记录开始时间
	startTime := time.Now()

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 计算响应时间
	responseTime := time.Since(startTime).Seconds()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API请求失败: %d - %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var qwenResp QwenResponse
	if err := json.Unmarshal(body, &qwenResp); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	// 提取文本内容
	var resultText string
	if len(qwenResp.Output.Choices) > 0 && len(qwenResp.Output.Choices[0].Message.Content) > 0 {
		resultText = qwenResp.Output.Choices[0].Message.Content[0].Text
	}

	// 记录日志
	fmt.Printf("qwen-vl-plus响应时间: %.2f秒\n", responseTime)
	fmt.Printf("qwen-vl-plus token消耗: %+v\n", qwenResp.Usage)
	fmt.Printf("qwen-vl-plus返回结果: %s\n", resultText)

	return &AnalyzeImageResult{
		Text:         resultText,
		ResponseTime: responseTime,
		TokenUsage:   qwenResp.Usage,
		RawResponse:  qwenResp,
	}, nil
}


