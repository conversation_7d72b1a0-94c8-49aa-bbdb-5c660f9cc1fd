"""
配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

# API配置
QWEN_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
DEEPSEEK_API_URL = "https://api.deepseek.com/chat/completions"

# API密钥 - 从环境变量获取
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")

# 模型配置
QWEN_MODEL = "qwen-vl-plus"
DEEPSEEK_MODEL = "deepseek-chat"

# 请求超时时间（秒）
REQUEST_TIMEOUT = 30

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
