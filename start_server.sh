#!/bin/bash

# 考试题目处理API服务器启动脚本

echo "=== 考试题目处理API服务器启动脚本 ==="

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "警告: .env文件不存在，请复制.env.example为.env并配置API密钥"
    echo "cp .env.example .env"
    echo "然后编辑.env文件填入真实的API密钥"
    exit 1
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 检查依赖是否安装
echo "检查依赖..."
pip install -r requirements.txt

# 启动API服务器
echo "启动API服务器..."
echo "服务器将在 http://localhost:5000 启动"
echo "按 Ctrl+C 停止服务器"
echo ""

python api_server.py
