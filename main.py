"""
考试题目处理主程序
"""
import sys
from typing import Dict, Any
from qwen_client import QwenClient
from deepseek_client import DeepSeekClient
from logger import APILogger

class ExamProcessor:
    """考试题目处理器"""
    
    def __init__(self):
        self.qwen_client = QwenClient()
        self.deepseek_client = DeepSeekClient()
    
    def process_exam_image(self, image_url: str) -> Dict[str, Any]:
        """
        处理考试图片
        
        Args:
            image_url: 图片URL
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 步骤1: 使用qwen-vl-plus识别图片中的题目
            qwen_result, qwen_time, qwen_tokens = self.qwen_client.analyze_image(image_url)
            
            # 步骤2: 使用deepseek-chat回答题目
            answer, explanation, deepseek_time, deepseek_tokens = self.deepseek_client.answer_question(qwen_result)
            
            # 记录最终结果
            APILogger.log_final_result(answer, explanation)
            
            # 返回完整结果
            result = {
                "success": True,
                "qwen_result": {
                    "content": qwen_result,
                    "response_time": qwen_time,
                    "token_usage": qwen_tokens
                },
                "deepseek_result": {
                    "answer": answer,
                    "explanation": explanation,
                    "response_time": deepseek_time,
                    "token_usage": deepseek_tokens
                },
                "final_answer": {
                    "correct_answer": answer,
                    "explanation": explanation
                }
            }
            
            return result
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            APILogger.log_error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python main.py <图片URL>")
        print("示例: python main.py https://example.com/exam_image.jpg")
        sys.exit(1)
    
    image_url = sys.argv[1]
    
    print("开始处理考试图片...")
    print(f"图片URL: {image_url}")
    print("-" * 50)
    
    processor = ExamProcessor()
    result = processor.process_exam_image(image_url)
    
    if result["success"]:
        print("\n处理完成！")
        print(f"正确答案: {result['final_answer']['correct_answer']}")
        print(f"答案解析: {result['final_answer']['explanation']}")
    else:
        print(f"\n处理失败: {result['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
