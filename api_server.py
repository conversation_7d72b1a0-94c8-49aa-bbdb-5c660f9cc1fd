"""
Web API服务器
提供HTTP接口用于处理考试图片
"""
from flask import Flask, request, jsonify
from main import ExamProcessor
from logger import APILogger

app = Flask(__name__)

@app.route('/process_exam', methods=['POST'])
def process_exam():
    """
    处理考试图片的API接口
    
    请求格式:
    {
        "image_url": "https://example.com/exam_image.jpg"
    }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        
        if not data or 'image_url' not in data:
            return jsonify({
                "success": False,
                "error": "缺少image_url参数"
            }), 400
        
        image_url = data['image_url']
        
        # 处理图片
        processor = ExamProcessor()
        result = processor.process_exam_image(image_url)
        
        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500
            
    except Exception as e:
        error_msg = f"API处理异常: {str(e)}"
        APILogger.log_error(error_msg)
        return jsonify({
            "success": False,
            "error": error_msg
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "message": "考试题目处理API服务正常运行"
    })

@app.route('/', methods=['GET'])
def index():
    """首页，显示API使用说明"""
    return jsonify({
        "service": "考试题目处理API",
        "version": "1.0.0",
        "endpoints": {
            "POST /process_exam": "处理考试图片",
            "GET /health": "健康检查",
            "GET /": "API说明"
        },
        "usage": {
            "url": "/process_exam",
            "method": "POST",
            "content_type": "application/json",
            "body": {
                "image_url": "https://example.com/exam_image.jpg"
            }
        }
    })

if __name__ == '__main__':
    port = 9999
    print("启动考试题目处理API服务...")
    print(f"访问 http://localhost:{port} 查看API说明")
    print(f"使用 POST http://localhost:{port}/process_exam 处理考试图片")
    app.run(host='0.0.0.0', port=port, debug=True)
