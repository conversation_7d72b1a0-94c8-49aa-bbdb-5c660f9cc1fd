#!/bin/bash

# Go版本测试脚本

echo "=== Go版本考试题目处理系统测试 ==="

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21或更高版本"
    exit 1
fi

echo "✅ Go版本: $(go version)"

# 检查环境变量
if [ -z "$QWEN_API_KEY" ]; then
    echo "⚠️  警告: QWEN_API_KEY环境变量未设置"
fi

if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "⚠️  警告: DEEPSEEK_API_KEY环境变量未设置"
fi

# 初始化Go模块
echo "📦 初始化Go模块..."
go mod tidy

# 编译检查
echo "🔨 编译检查..."
if go build -o exam-processor-go .; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi

# 运行测试（如果提供了图片URL）
if [ $# -eq 1 ]; then
    echo "🧪 运行测试..."
    echo "图片URL: $1"
    echo "开始处理..."
    ./exam-processor-go "$1"
else
    echo "💡 使用方法:"
    echo "  $0 <图片URL>"
    echo ""
    echo "示例:"
    echo "  $0 https://lnterstellar.oss-cn-zhangjiakou.aliyuncs.com/questions/1748988233329.jpg"
fi

echo ""
echo "=== 测试完成 ==="
