package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// DeepSeekClient DeepSeek-Chat API客户端
type DeepSeekClient struct {
	APIURL string
	APIKey string
	Model  string
}

// DeepSeekTokenUsage DeepSeek token使用情况
type DeepSeekTokenUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// DeepSeekResponse DeepSeek API响应结构
type DeepSeekResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage DeepSeekTokenUsage `json:"usage"`
}

// DeepSeekRequest DeepSeek API请求结构
type DeepSeekRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	Stream bool `json:"stream"`
}

// AnswerResult 回答结果
type AnswerResult struct {
	Answer       string
	Explanation  string
	ResponseTime float64
	TokenUsage   DeepSeekTokenUsage
	RawResponse  DeepSeekResponse
}

// NewDeepSeekClient 创建新的DeepSeek客户端
func NewDeepSeekClient() (*DeepSeekClient, error) {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("DEEPSEEK_API_KEY环境变量未设置")
	}

	return &DeepSeekClient{
		APIURL: "https://api.deepseek.com/chat/completions",
		APIKey: apiKey,
		Model:  "deepseek-chat",
	}, nil
}

// AnswerQuestion 回答考试题目
func (c *DeepSeekClient) AnswerQuestion(questionContent string) (*AnswerResult, error) {
	fmt.Printf("发送请求到deepseek-chat，输入内容: %s...\n", questionContent[:min(200, len(questionContent))])

	// 构建提示词
	prompt := fmt.Sprintf(`以下是一道考试题目，请分析并给出正确答案和详细解析。

题目内容：
%s

请按照以下格式回答：
【正确答案】：（给出正确的选项，如A、B、C、D或Y、N，如果是多选题请给出所有正确选项）
【答案解析】：（详细解释为什么这个答案是正确的，包括相关的法规、原理或知识点）

要求：
1. 答案必须准确
2. 解析要详细且有依据
3. 如果是驾驶考试题，请结合交通法规进行解析
4. 格式严格按照要求输出`, questionContent)

	// 构建请求
	request := DeepSeekRequest{
		Model: c.Model,
		Messages: []struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		}{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stream: false,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", c.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// 记录开始时间
	startTime := time.Now()

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 计算响应时间
	responseTime := time.Since(startTime).Seconds()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API请求失败: %d - %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var deepseekResp DeepSeekResponse
	if err := json.Unmarshal(body, &deepseekResp); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	// 提取响应内容
	var resultText string
	if len(deepseekResp.Choices) > 0 {
		resultText = deepseekResp.Choices[0].Message.Content
	}

	// 解析答案和解析
	answer, explanation := parseResponse(resultText)

	// 记录日志
	fmt.Printf("deepseek-chat响应时间: %.2f秒\n", responseTime)
	fmt.Printf("deepseek-chat token消耗: %+v\n", deepseekResp.Usage)
	fmt.Printf("deepseek-chat返回结果: %s\n", resultText)

	return &AnswerResult{
		Answer:       answer,
		Explanation:  explanation,
		ResponseTime: responseTime,
		TokenUsage:   deepseekResp.Usage,
		RawResponse:  deepseekResp,
	}, nil
}

// parseResponse 解析DeepSeek的响应，提取答案和解析
func parseResponse(responseText string) (string, string) {
	var answer, explanation string
	
	lines := strings.Split(responseText, "\n")
	currentSection := ""
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "【正确答案】") {
			currentSection = "answer"
			answer = strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(line, "【正确答案】", ""), "：", ""))
			answer = strings.TrimSpace(strings.ReplaceAll(answer, ":", ""))
		} else if strings.Contains(line, "【答案解析】") {
			currentSection = "explanation"
			explanation = strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(line, "【答案解析】", ""), "：", ""))
			explanation = strings.TrimSpace(strings.ReplaceAll(explanation, ":", ""))
		} else if currentSection == "answer" && line != "" {
			answer += " " + line
		} else if currentSection == "explanation" && line != "" {
			explanation += " " + line
		}
	}
	
	return strings.TrimSpace(answer), strings.TrimSpace(explanation)
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
