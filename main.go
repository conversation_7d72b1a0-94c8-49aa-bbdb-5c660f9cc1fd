package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// ExamProcessor 考试题目处理器
type ExamProcessor struct {
	qwenClient     *QwenClient
	deepseekClient *DeepSeekClient
}

// ProcessResult 处理结果
type ProcessResult struct {
	Success      bool                `json:"success"`
	QwenResult   *QwenResultData     `json:"qwen_result,omitempty"`
	DeepSeekResult *DeepSeekResultData `json:"deepseek_result,omitempty"`
	FinalAnswer  *FinalAnswerData    `json:"final_answer,omitempty"`
	Error        string              `json:"error,omitempty"`
}

// QwenResultData qwen结果数据
type QwenResultData struct {
	Content      string      `json:"content"`
	ResponseTime float64     `json:"response_time"`
	TokenUsage   TokenUsage  `json:"token_usage"`
	RawResponse  interface{} `json:"raw_response"`
}

// DeepSeekResultData deepseek结果数据
type DeepSeekResultData struct {
	Answer       string             `json:"answer"`
	Explanation  string             `json:"explanation"`
	ResponseTime float64            `json:"response_time"`
	TokenUsage   DeepSeekTokenUsage `json:"token_usage"`
	RawResponse  interface{}        `json:"raw_response"`
}

// FinalAnswerData 最终答案数据
type FinalAnswerData struct {
	CorrectAnswer string `json:"correct_answer"`
	Explanation   string `json:"explanation"`
}

// NewExamProcessor 创建新的考试处理器
func NewExamProcessor() (*ExamProcessor, error) {
	qwenClient, err := NewQwenClient()
	if err != nil {
		return nil, fmt.Errorf("创建Qwen客户端失败: %v", err)
	}

	deepseekClient, err := NewDeepSeekClient()
	if err != nil {
		return nil, fmt.Errorf("创建DeepSeek客户端失败: %v", err)
	}

	return &ExamProcessor{
		qwenClient:     qwenClient,
		deepseekClient: deepseekClient,
	}, nil
}

// ProcessExamImage 处理考试图片
func (p *ExamProcessor) ProcessExamImage(imageURL string) *ProcessResult {
	fmt.Println("开始处理考试图片...")
	fmt.Printf("图片URL: %s\n", imageURL)
	fmt.Println("--------------------------------------------------")

	// 步骤1: 使用qwen-vl-plus识别图片中的题目
	qwenResult, err := p.qwenClient.AnalyzeImage(imageURL)
	if err != nil {
		return &ProcessResult{
			Success: false,
			Error:   fmt.Sprintf("qwen分析失败: %v", err),
		}
	}

	// 步骤2: 使用deepseek-chat回答题目
	deepseekResult, err := p.deepseekClient.AnswerQuestion(qwenResult.Text)
	if err != nil {
		return &ProcessResult{
			Success: false,
			Error:   fmt.Sprintf("deepseek分析失败: %v", err),
		}
	}

	// 记录最终结果
	fmt.Println("==================================================")
	fmt.Println("最终处理结果:")
	fmt.Printf("正确答案: %s\n", deepseekResult.Answer)
	fmt.Printf("答案解析: %s\n", deepseekResult.Explanation)
	fmt.Println("==================================================")

	return &ProcessResult{
		Success: true,
		QwenResult: &QwenResultData{
			Content:      qwenResult.Text,
			ResponseTime: qwenResult.ResponseTime,
			TokenUsage:   qwenResult.TokenUsage,
			RawResponse:  qwenResult.RawResponse,
		},
		DeepSeekResult: &DeepSeekResultData{
			Answer:       deepseekResult.Answer,
			Explanation:  deepseekResult.Explanation,
			ResponseTime: deepseekResult.ResponseTime,
			TokenUsage:   deepseekResult.TokenUsage,
			RawResponse:  deepseekResult.RawResponse,
		},
		FinalAnswer: &FinalAnswerData{
			CorrectAnswer: deepseekResult.Answer,
			Explanation:   deepseekResult.Explanation,
		},
	}
}

func main() {
	if len(os.Args) != 2 {
		fmt.Println("使用方法: go run . <图片URL>")
		fmt.Println("示例: go run . https://example.com/exam_image.jpg")
		os.Exit(1)
	}

	imageURL := os.Args[1]

	// 创建处理器
	processor, err := NewExamProcessor()
	if err != nil {
		fmt.Printf("创建处理器失败: %v\n", err)
		os.Exit(1)
	}

	// 处理图片
	result := processor.ProcessExamImage(imageURL)

	if result.Success {
		fmt.Println("\n处理完成！")
		fmt.Printf("正确答案: %s\n", result.FinalAnswer.CorrectAnswer)
		fmt.Printf("答案解析: %s\n", result.FinalAnswer.Explanation)

		// 输出完整的JSON结果（可选）
		if jsonData, err := json.MarshalIndent(result, "", "  "); err == nil {
			fmt.Println("\n完整JSON结果:")
			fmt.Println(string(jsonData))
		}
	} else {
		fmt.Printf("\n处理失败: %s\n", result.Error)
		os.Exit(1)
	}
}
