"""
日志记录模块
"""
import logging
import time
from typing import Dict, Any
from config import LOG_LEVEL, LOG_FORMAT

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler('exam_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class APILogger:
    """API调用日志记录器"""
    
    @staticmethod
    def log_qwen_request(image_url: str):
        """记录qwen请求"""
        logger.info(f"发送请求到qwen-vl-plus，图片URL: {image_url}")
    
    @staticmethod
    def log_qwen_response(response_data: Dict[Any, Any], response_time: float, token_usage: Dict[str, int]):
        """记录qwen响应"""
        logger.info(f"qwen-vl-plus响应时间: {response_time:.2f}秒")
        logger.info(f"qwen-vl-plus token消耗: {token_usage}")
        logger.info(f"qwen-vl-plus返回结果: {response_data}")
    
    @staticmethod
    def log_deepseek_request(qwen_result: str):
        """记录deepseek请求"""
        logger.info(f"发送请求到deepseek-chat，输入内容: {qwen_result[:200]}...")
    
    @staticmethod
    def log_deepseek_response(response_data: Dict[Any, Any], response_time: float, token_usage: Dict[str, int]):
        """记录deepseek响应"""
        logger.info(f"deepseek-chat响应时间: {response_time:.2f}秒")
        logger.info(f"deepseek-chat token消耗: {token_usage}")
        logger.info(f"deepseek-chat返回结果: {response_data}")
    
    @staticmethod
    def log_error(error_msg: str, error_type: str = "ERROR"):
        """记录错误"""
        logger.error(f"{error_type}: {error_msg}")
    
    @staticmethod
    def log_final_result(answer: str, explanation: str):
        """记录最终结果"""
        logger.info("=" * 50)
        logger.info("最终处理结果:")
        logger.info(f"正确答案: {answer}")
        logger.info(f"答案解析: {explanation}")
        logger.info("=" * 50)
