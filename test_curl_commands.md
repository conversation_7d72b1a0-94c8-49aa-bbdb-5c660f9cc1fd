# cURL测试命令

## 启动API服务器

首先启动API服务器：

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动API服务器
python api_server.py
```

服务器将在 http://localhost:5000 启动

## cURL测试命令

### 1. 健康检查

```bash
curl -X GET http://localhost:5000/health
```

### 2. 查看API说明

```bash
curl -X GET http://localhost:5000/
```

### 3. 处理考试图片（主要功能）

```bash
curl -X POST http://localhost:5000/process_exam \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/exam_image.jpg"
  }'
```

### 4. 测试示例（使用真实图片URL）

```bash
# 请替换为真实的考试图片URL
curl -X POST http://localhost:5000/process_exam \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://your-image-host.com/driving_exam.jpg"
  }'
```

### 5. 带格式化输出的测试

```bash
curl -X POST http://localhost:5000/process_exam \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/exam_image.jpg"
  }' | python -m json.tool
```

## 预期响应格式

### 成功响应示例：

```json
{
  "success": true,
  "qwen_result": {
    "content": "题目类型：单选题\n题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？\n选项内容：\nA、减速鸣喇叭示意\nB、迅速行驶到坡顶以改善视距\nC、长时间开启远光灯提醒对向来车\nD、不间断鸣喇叭并加速冲坡",
    "response_time": 2.34,
    "token_usage": {
      "input_tokens": 150,
      "output_tokens": 80,
      "total_tokens": 230
    },
    "raw_response": {
      "output": {
        "choices": [
          {
            "finish_reason": "stop",
            "message": {
              "role": "assistant",
              "content": "题目类型：单选题\n题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？\n选项内容：\nA、减速鸣喇叭示意\nB、迅速行驶到坡顶以改善视距\nC、长时间开启远光灯提醒对向来车\nD、不间断鸣喇叭并加速冲坡"
            }
          }
        ]
      },
      "usage": {
        "input_tokens": 150,
        "output_tokens": 80,
        "total_tokens": 230
      },
      "request_id": "qwen-request-id-123"
    }
  },
  "deepseek_result": {
    "answer": "A",
    "explanation": "根据交通安全法规，在视距不良的坡道顶端，应当减速慢行并鸣喇叭示意，以提醒对向来车注意避让。",
    "response_time": 1.56,
    "token_usage": {
      "prompt_tokens": 200,
      "completion_tokens": 120,
      "total_tokens": 320
    },
    "raw_response": {
      "id": "deepseek-response-id-456",
      "object": "chat.completion",
      "created": 1703123456,
      "model": "deepseek-chat",
      "choices": [
        {
          "index": 0,
          "message": {
            "role": "assistant",
            "content": "【正确答案】：A\n【答案解析】：根据交通安全法规，在视距不良的坡道顶端，应当减速慢行并鸣喇叭示意，以提醒对向来车注意避让。"
          },
          "finish_reason": "stop"
        }
      ],
      "usage": {
        "prompt_tokens": 200,
        "completion_tokens": 120,
        "total_tokens": 320
      }
    }
  },
  "final_answer": {
    "correct_answer": "A",
    "explanation": "根据交通安全法规，在视距不良的坡道顶端，应当减速慢行并鸣喇叭示意，以提醒对向来车注意避让。"
  }
}
```

### 错误响应示例：

```json
{
  "success": false,
  "error": "缺少image_url参数"
}
```

## 注意事项

1. 确保已经配置了`.env`文件中的API密钥
2. 图片URL必须是公开可访问的
3. 图片应该包含清晰的考试题目内容
4. API调用可能需要一些时间，请耐心等待响应
