"""
系统测试脚本
用于验证各个模块是否正常工作
"""
import os
import sys
from config import QWEN_API_KEY, DEEPSEEK_API_KEY

def test_environment():
    """测试环境配置"""
    print("=== 环境配置测试 ===")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ 虚拟环境已激活")
    else:
        print("⚠ 警告: 未检测到虚拟环境")
    
    # 检查依赖包
    try:
        import requests
        import flask
        from dotenv import load_dotenv
        print("✓ 所有依赖包已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        return False
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✓ .env文件存在")
    else:
        print("⚠ 警告: .env文件不存在，请复制.env.example为.env")
    
    # 检查API密钥
    if QWEN_API_KEY and QWEN_API_KEY != "your_qwen_api_key_here":
        print("✓ Qwen API密钥已配置")
    else:
        print("⚠ 警告: Qwen API密钥未配置")
    
    if DEEPSEEK_API_KEY and DEEPSEEK_API_KEY != "your_deepseek_api_key_here":
        print("✓ DeepSeek API密钥已配置")
    else:
        print("⚠ 警告: DeepSeek API密钥未配置")
    
    return True

def test_modules():
    """测试模块导入"""
    print("\n=== 模块导入测试 ===")
    
    try:
        from qwen_client import QwenClient
        print("✓ QwenClient模块导入成功")
    except Exception as e:
        print(f"✗ QwenClient模块导入失败: {e}")
        return False
    
    try:
        from deepseek_client import DeepSeekClient
        print("✓ DeepSeekClient模块导入成功")
    except Exception as e:
        print(f"✗ DeepSeekClient模块导入失败: {e}")
        return False
    
    try:
        from main import ExamProcessor
        print("✓ ExamProcessor模块导入成功")
    except Exception as e:
        print(f"✗ ExamProcessor模块导入失败: {e}")
        return False
    
    try:
        from logger import APILogger
        print("✓ APILogger模块导入成功")
    except Exception as e:
        print(f"✗ APILogger模块导入失败: {e}")
        return False
    
    return True

def test_api_clients():
    """测试API客户端初始化"""
    print("\n=== API客户端测试 ===")
    
    try:
        from qwen_client import QwenClient
        if QWEN_API_KEY and QWEN_API_KEY != "your_qwen_api_key_here":
            client = QwenClient()
            print("✓ QwenClient初始化成功")
        else:
            print("⚠ 跳过QwenClient初始化（API密钥未配置）")
    except Exception as e:
        print(f"✗ QwenClient初始化失败: {e}")
    
    try:
        from deepseek_client import DeepSeekClient
        if DEEPSEEK_API_KEY and DEEPSEEK_API_KEY != "your_deepseek_api_key_here":
            client = DeepSeekClient()
            print("✓ DeepSeekClient初始化成功")
        else:
            print("⚠ 跳过DeepSeekClient初始化（API密钥未配置）")
    except Exception as e:
        print(f"✗ DeepSeekClient初始化失败: {e}")

def main():
    """主测试函数"""
    print("考试题目处理系统 - 系统测试")
    print("=" * 50)
    
    # 运行各项测试
    env_ok = test_environment()
    modules_ok = test_modules()
    
    if env_ok and modules_ok:
        test_api_clients()
    
    print("\n=== 测试完成 ===")
    
    if not env_ok or not modules_ok:
        print("⚠ 发现问题，请检查上述错误信息")
        return False
    
    print("✓ 基础测试通过")
    
    # 提供下一步指导
    print("\n=== 下一步操作 ===")
    if not os.path.exists('.env'):
        print("1. 复制.env.example为.env: cp .env.example .env")
        print("2. 编辑.env文件，填入真实的API密钥")
    
    print("3. 启动API服务器: python api_server.py")
    print("4. 或使用启动脚本: ./start_server.sh")
    print("5. 使用cURL测试API（参考test_curl_commands.md）")
    
    return True

if __name__ == "__main__":
    main()
