# Go版本考试题目处理系统

这是Python版本的Go语言实现，提供相同的功能：使用qwen-vl-plus进行图片识别，使用deepseek-chat进行题目解答。

## 功能特性

- ✅ 使用qwen-vl-plus识别考试图片中的题目内容
- ✅ 使用deepseek-chat提供准确的答案和解析
- ✅ 完整的日志记录，包括响应时间和token消耗统计
- ✅ 支持单选题、多选题、判断题三种题型
- ✅ **返回原始数据**：同时返回qwen和deepseek的完整原始响应数据

## 文件结构

```
.
├── main.go           # 主程序入口
├── qwen_client.go    # Qwen-VL-Plus API客户端
├── deepseek_client.go # DeepSeek-Chat API客户端
├── go.mod           # Go模块文件
└── README_GO.md     # Go版本说明文档
```

## 安装和配置

### 1. 环境要求
- Go 1.21 或更高版本

### 2. 设置环境变量
```bash
export QWEN_API_KEY="your_qwen_api_key_here"
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"
```

或者在Windows中：
```cmd
set QWEN_API_KEY=your_qwen_api_key_here
set DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

### 3. 初始化Go模块
```bash
go mod tidy
```

## 使用方法

### 命令行使用
```bash
go run . <图片URL>
```

示例：
```bash
go run . https://lnterstellar.oss-cn-zhangjiakou.aliyuncs.com/questions/1748988233329.jpg
```

### 编译后使用
```bash
# 编译
go build -o exam-processor

# 运行
./exam-processor https://example.com/exam_image.jpg
```

## 代码结构说明

### 1. QwenClient (qwen_client.go)
- `QwenClient` 结构体：封装qwen API调用
- `AnalyzeImage()` 方法：分析图片中的考试题目
- 返回识别结果、响应时间、token使用情况和原始响应数据

### 2. DeepSeekClient (deepseek_client.go)
- `DeepSeekClient` 结构体：封装deepseek API调用
- `AnswerQuestion()` 方法：回答考试题目
- 返回答案、解析、响应时间、token使用情况和原始响应数据

### 3. ExamProcessor (main.go)
- `ExamProcessor` 结构体：整合两个客户端
- `ProcessExamImage()` 方法：完整的处理流程
- 返回包含所有信息的结构化结果

## 关键数据结构

### Qwen请求Payload
```go
type QwenRequest struct {
    Model string `json:"model"`
    Input struct {
        Messages []struct {
            Role    string `json:"role"`
            Content []struct {
                Image string `json:"image,omitempty"`
                Text  string `json:"text,omitempty"`
            } `json:"content"`
        } `json:"messages"`
    } `json:"input"`
    Parameters struct {
        ResultFormat string `json:"result_format"`
    } `json:"parameters"`
}
```

### DeepSeek请求Payload
```go
type DeepSeekRequest struct {
    Model    string `json:"model"`
    Messages []struct {
        Role    string `json:"role"`
        Content string `json:"content"`
    } `json:"messages"`
    Stream bool `json:"stream"`
}
```

## 输出格式

程序会输出详细的处理日志和最终的JSON结果：

```json
{
  "success": true,
  "qwen_result": {
    "content": "识别的题目内容...",
    "response_time": 2.34,
    "token_usage": {
      "input_tokens": 1005,
      "output_tokens": 143,
      "total_tokens": 1148
    },
    "raw_response": { ... }
  },
  "deepseek_result": {
    "answer": "A",
    "explanation": "详细的答案解析...",
    "response_time": 1.56,
    "token_usage": {
      "prompt_tokens": 254,
      "completion_tokens": 357,
      "total_tokens": 611
    },
    "raw_response": { ... }
  },
  "final_answer": {
    "correct_answer": "A",
    "explanation": "详细的答案解析..."
  }
}
```

## 与Python版本的对比

| 特性 | Python版本 | Go版本 |
|------|------------|--------|
| 基本功能 | ✅ | ✅ |
| API调用 | ✅ | ✅ |
| 日志记录 | ✅ | ✅ |
| 原始数据返回 | ✅ | ✅ |
| Web API | ✅ | ❌ (可扩展) |
| 性能 | 中等 | 更高 |
| 内存使用 | 较高 | 较低 |
| 部署 | 需要Python环境 | 单一可执行文件 |

## 扩展建议

1. **添加Web API**: 可以使用gin或echo框架添加HTTP服务
2. **配置文件**: 添加配置文件支持，而不是只依赖环境变量
3. **错误处理**: 增强错误处理和重试机制
4. **并发处理**: 添加并发处理多个图片的能力
5. **缓存机制**: 添加结果缓存以提高性能

## 注意事项

1. 确保设置了正确的API密钥环境变量
2. 图片URL必须公开可访问
3. 网络连接稳定，API调用可能需要一些时间
4. Go版本使用标准库，无需额外依赖
