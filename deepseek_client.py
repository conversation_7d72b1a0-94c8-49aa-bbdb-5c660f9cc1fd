"""
DeepSeek-Chat API客户端
"""
import requests
import time
from typing import Dict, Any, <PERSON><PERSON>
from config import DEEPSEEK_API_URL, DEEPSEEK_API_KEY, DEEPSEEK_MODEL, REQUEST_TIMEOUT
from logger import APILogger

class DeepSeekClient:
    """DeepSeek-Chat API客户端"""
    
    def __init__(self):
        self.api_url = DEEPSEEK_API_URL
        self.api_key = DEEPSEEK_API_KEY
        self.model = DEEPSEEK_MODEL
        
        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY环境变量未设置")
    
    def answer_question(self, question_content: str) -> Tuple[str, str, float, Dict[str, int], Dict[str, Any]]:
        """
        回答考试题目
        
        Args:
            question_content: qwen识别的题目内容
            
        Returns:
            Tuple[str, str, float, Dict[str, int], Dict[str, Any]]: (正确答案, 答案解析, 响应时间, token使用情况, 原始响应数据)
        """
        APILogger.log_deepseek_request(question_content)
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        prompt = f"""以下是一道考试题目，请分析并给出正确答案和详细解析。

题目内容：
{question_content}

请按照以下格式回答：
【正确答案】：（给出正确的选项，如A、B、C、D或Y、N，如果是多选题请给出所有正确选项）
【答案解析】：（详细解释为什么这个答案是正确的，包括相关的法规、原理或知识点）

要求：
1. 答案必须准确
2. 解析要详细且有依据
3. 如果是驾驶考试题，请结合交通法规进行解析
4. 格式严格按照要求输出"""

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "stream": False
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 提取结果
                result_text = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # 提取token使用情况
                usage = response_data.get("usage", {})
                token_usage = {
                    "prompt_tokens": usage.get("prompt_tokens", 0),
                    "completion_tokens": usage.get("completion_tokens", 0),
                    "total_tokens": usage.get("total_tokens", 0)
                }
                
                APILogger.log_deepseek_response(response_data, response_time, token_usage)

                # 解析答案和解析
                answer, explanation = self._parse_response(result_text)

                return answer, explanation, response_time, token_usage, response_data
            else:
                error_msg = f"DeepSeek API请求失败: {response.status_code} - {response.text}"
                APILogger.log_error(error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"DeepSeek API请求异常: {str(e)}"
            APILogger.log_error(error_msg)
            raise Exception(error_msg)
    
    def _parse_response(self, response_text: str) -> Tuple[str, str]:
        """
        解析DeepSeek的响应，提取答案和解析
        
        Args:
            response_text: DeepSeek的响应文本
            
        Returns:
            Tuple[str, str]: (正确答案, 答案解析)
        """
        answer = ""
        explanation = ""
        
        lines = response_text.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if '【正确答案】' in line:
                current_section = 'answer'
                answer = line.replace('【正确答案】', '').replace('：', '').replace(':', '').strip()
            elif '【答案解析】' in line:
                current_section = 'explanation'
                explanation = line.replace('【答案解析】', '').replace('：', '').replace(':', '').strip()
            elif current_section == 'answer' and line:
                answer += ' ' + line
            elif current_section == 'explanation' and line:
                explanation += ' ' + line
        
        return answer.strip(), explanation.strip()
